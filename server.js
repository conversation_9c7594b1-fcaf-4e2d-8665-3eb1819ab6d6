const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const mysql = require('mysql2/promise');
const jwt = require('jsonwebtoken');
require('dotenv').config();

const app = express();
const server = http.createServer(app);

// CORS配置
app.use(cors({
  origin: ["http://localhost:3000", "http://demo.lumao.cc", "http://admin.lumao.cc"],
  credentials: true
}));

// Socket.io配置
const io = socketIo(server, {
  cors: {
    origin: ["http://localhost:3000", "http://demo.lumao.cc", "http://admin.lumao.cc"],
    methods: ["GET", "POST"],
    credentials: true
  }
});

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'kaifa',
  password: process.env.DB_PASSWORD || 'kaifa',
  database: process.env.DB_DATABASE || 'kaifa',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

const pool = mysql.createPool(dbConfig);

// JWT密钥（应该与Laravel保持一致）
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// 存储在线用户
const onlineUsers = new Map();
const userSockets = new Map();

// 验证JWT Token (支持普通用户和管理员)
async function verifyToken(token, userType = 'user') {
  try {
    // 移除Bearer前缀
    if (token.startsWith('Bearer ')) {
      token = token.substring(7);
    }

    console.log('Verifying token:', token.substring(0, 20) + '...', 'Type:', userType);

    if (userType === 'admin') {
      // 管理员token验证 (简化处理，实际项目中应该使用更安全的方式)
      // 这里假设管理员token格式为 admin:{admin_id}:{session_token}
      if (token.startsWith('admin:')) {
        const parts = token.split(':');
        if (parts.length >= 2) {
          const adminId = parts[1];

          // 查询管理员信息
          const [adminRows] = await pool.execute(
            'SELECT * FROM admin_users WHERE id = ?',
            [adminId]
          );

          if (adminRows.length > 0) {
            const admin = adminRows[0];
            console.log('Admin token verification successful for:', admin.name);
            return {
              id: admin.id,
              name: admin.name,
              username: admin.username,
              user_type: 'admin',
              is_admin: true
            };
          }
        }
      }
      return null;
    }

    // 普通用户Sanctum token验证逻辑
    const tokenParts = token.split('|');
    if (tokenParts.length !== 2) {
      console.log('Invalid token format');
      return null;
    }

    const [tokenId, plainTextToken] = tokenParts;
    console.log('Token ID:', tokenId, 'Plain text token length:', plainTextToken.length);

    // 查询token记录
    const [tokenRows] = await pool.execute(
      'SELECT * FROM personal_access_tokens WHERE id = ? AND (expires_at IS NULL OR expires_at > NOW())',
      [tokenId]
    );

    if (tokenRows.length === 0) {
      console.log('Token not found or expired');
      return null;
    }

    const tokenRecord = tokenRows[0];
    console.log('Found token record for user:', tokenRecord.tokenable_id);

    // 验证token哈希
    const crypto = require('crypto');
    const hashedToken = crypto.createHash('sha256').update(plainTextToken).digest('hex');

    if (hashedToken !== tokenRecord.token) {
      console.log('Token hash mismatch');
      return null;
    }

    // 获取用户信息
    const [userRows] = await pool.execute(
      'SELECT * FROM users WHERE id = ?',
      [tokenRecord.tokenable_id]
    );

    if (userRows.length > 0) {
      const user = userRows[0];
      console.log('Token verification successful for user:', user.name);
      return {
        ...user,
        user_type: 'user',
        is_admin: false
      };
    }

    return null;
  } catch (error) {
    console.error('Token verification error:', error);
    return null;
  }
}

// 验证用户是否有权限访问订单聊天
async function verifyOrderAccess(userId, orderId, isAdmin = false) {
  try {
    // 管理员可以访问任何订单的聊天
    if (isAdmin) {
      const [orderRows] = await pool.execute(
        'SELECT id FROM otc_orders WHERE id = ?',
        [orderId]
      );
      return orderRows.length > 0;
    }

    // 普通用户只能访问自己参与的订单
    const [rows] = await pool.execute(
      'SELECT * FROM otc_orders WHERE id = ? AND (buyer_id = ? OR seller_id = ?)',
      [orderId, userId, userId]
    );

    return rows.length > 0;
  } catch (error) {
    console.error('Order access verification error:', error);
    return false;
  }
}

// 保存消息到数据库
async function saveMessage(orderId, userId, type, content, attachments = null, userType = 'user') {
  try {
    const [result] = await pool.execute(
      'INSERT INTO order_messages (order_id, user_id, user_type, type, content, attachments, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())',
      [orderId, userId, userType, type, content, JSON.stringify(attachments)]
    );

    // 获取完整的消息数据
    let messageQuery, messageParams;

    if (userType === 'admin') {
      messageQuery = `
        SELECT om.*, au.name as user_name, au.username as user_email
        FROM order_messages om
        LEFT JOIN admin_users au ON om.user_id = au.id
        WHERE om.id = ?
      `;
    } else if (userType === 'system') {
      messageQuery = `
        SELECT om.*, 'System' as user_name, '<EMAIL>' as user_email
        FROM order_messages om
        WHERE om.id = ?
      `;
    } else {
      messageQuery = `
        SELECT om.*, u.name as user_name, u.email as user_email
        FROM order_messages om
        LEFT JOIN users u ON om.user_id = u.id
        WHERE om.id = ?
      `;
    }

    const [messageRows] = await pool.execute(messageQuery, [result.insertId]);

    return messageRows[0];
  } catch (error) {
    console.error('Save message error:', error);
    return null;
  }
}

// 获取订单聊天历史记录
async function getChatHistory(orderId, limit = 100) {
  try {
    // 分别查询不同类型的消息，然后合并
    const userMessagesQuery = `
      SELECT om.*, u.name as user_name, u.email as user_email
      FROM order_messages om
      LEFT JOIN users u ON om.user_id = u.id
      WHERE om.order_id = ? AND om.user_type = 'user'
    `;

    const adminMessagesQuery = `
      SELECT om.*, au.name as user_name, au.username as user_email
      FROM order_messages om
      LEFT JOIN admin_users au ON om.user_id = au.id
      WHERE om.order_id = ? AND om.user_type = 'admin'
    `;

    const systemMessagesQuery = `
      SELECT om.*, 'System' as user_name, '<EMAIL>' as user_email
      FROM order_messages om
      WHERE om.order_id = ? AND om.user_type = 'system'
    `;

    const [userRows] = await pool.execute(userMessagesQuery, [orderId]);
    const [adminRows] = await pool.execute(adminMessagesQuery, [orderId]);
    const [systemRows] = await pool.execute(systemMessagesQuery, [orderId]);

    // 合并所有消息
    const allMessages = [...userRows, ...adminRows, ...systemRows];

    // 按时间排序
    allMessages.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

    // 限制数量
    const limitedMessages = allMessages.slice(-limit);

    return limitedMessages.map(row => {
      let attachments = null;
      try {
        if (row.attachments && typeof row.attachments === 'string') {
          attachments = JSON.parse(row.attachments);
        } else if (row.attachments && typeof row.attachments === 'object') {
          attachments = row.attachments;
        }
      } catch (e) {
        console.log('Failed to parse attachments:', row.attachments);
        attachments = null;
      }

      return {
        id: row.id,
        orderId: row.order_id,
        userId: row.user_id,
        userName: row.user_name || 'Unknown',
        userEmail: row.user_email || '',
        userType: row.user_type,
        type: row.type,
        content: row.content,
        attachments: attachments,
        createdAt: row.created_at,
        isRead: row.is_read
      };
    });
  } catch (error) {
    console.error('Get chat history error:', error);
    return [];
  }
}

// 获取订单的其他用户ID
async function getOtherUserId(orderId, currentUserId) {
  try {
    const [rows] = await pool.execute(
      'SELECT buyer_id, seller_id FROM otc_orders WHERE id = ?',
      [orderId]
    );
    
    if (rows.length > 0) {
      const order = rows[0];
      return currentUserId === order.buyer_id ? order.seller_id : order.buyer_id;
    }
    
    return null;
  } catch (error) {
    console.error('Get other user ID error:', error);
    return null;
  }
}

// Socket.io连接处理
io.on('connection', (socket) => {
  console.log('New client connected:', socket.id);
  
  // 用户认证
  socket.on('authenticate', async (data) => {
    try {
      const { token, userType = 'user' } = data;
      const user = await verifyToken(token, userType);

      if (user) {
        socket.userId = user.id;
        socket.userName = user.name;
        socket.userType = user.user_type || 'user';
        socket.isAdmin = user.is_admin || false;

        // 存储用户socket映射
        const userKey = `${user.user_type || 'user'}_${user.id}`;
        onlineUsers.set(userKey, socket.id);
        userSockets.set(socket.id, userKey);

        socket.emit('authenticated', {
          success: true,
          user: {
            id: user.id,
            name: user.name,
            username: user.username || user.name,
            email: user.email || '',
            userType: user.user_type || 'user',
            isAdmin: user.is_admin || false
          }
        });

        console.log(`${user.user_type === 'admin' ? 'Admin' : 'User'} ${user.name} (${user.id}) authenticated`);
      } else {
        socket.emit('authenticated', {
          success: false,
          message: '认证失败'
        });
      }
    } catch (error) {
      console.error('Authentication error:', error);
      socket.emit('authenticated', {
        success: false,
        message: '认证错误'
      });
    }
  });
  
  // 加入订单聊天室
  socket.on('join_order_chat', async (data) => {
    try {
      const { orderId } = data;
      
      if (!socket.userId) {
        socket.emit('error', { message: '请先认证' });
        return;
      }
      
      // 验证用户是否有权限访问此订单
      const hasAccess = await verifyOrderAccess(socket.userId, orderId, socket.isAdmin);
      if (!hasAccess) {
        socket.emit('error', { message: '无权限访问此订单聊天' });
        return;
      }

      // 加入房间
      const roomName = `order_${orderId}`;
      socket.join(roomName);
      socket.currentOrderId = orderId;

      // 获取聊天历史记录（管理员可以看到全部历史）
      const chatHistory = await getChatHistory(orderId, socket.isAdmin ? 200 : 50);

      socket.emit('joined_order_chat', {
        success: true,
        orderId: orderId,
        message: socket.isAdmin ? '已加入订单聊天，正在加载全部聊天记录...' : '已加入订单聊天',
        messages: chatHistory
      });

      // 如果是管理员加入，发送系统通知
      if (socket.isAdmin) {
        const systemMessage = await saveMessage(
          orderId,
          socket.userId,
          'system',
          `管理员 ${socket.userName} 已加入聊天，协助处理订单问题。`,
          null,
          'admin'
        );

        if (systemMessage) {
          io.to(roomName).emit('new_message', {
            id: systemMessage.id,
            orderId: systemMessage.order_id,
            userId: systemMessage.user_id,
            userName: systemMessage.user_name,
            userEmail: systemMessage.user_email,
            userType: 'admin',
            type: 'system',
            content: systemMessage.content,
            attachments: systemMessage.attachments ? JSON.parse(systemMessage.attachments) : null,
            createdAt: systemMessage.created_at,
            isRead: false
          });
        }
      }

      console.log(`${socket.isAdmin ? 'Admin' : 'User'} ${socket.userId} joined order ${orderId} chat`);
    } catch (error) {
      console.error('Join order chat error:', error);
      socket.emit('error', { message: '加入聊天失败' });
    }
  });
  
  // 发送消息
  socket.on('send_message', async (data) => {
    try {
      const { orderId, type, content, attachments } = data;
      
      if (!socket.userId) {
        socket.emit('error', { message: '请先认证' });
        return;
      }
      
      // 验证用户是否有权限发送消息到此订单
      const hasAccess = await verifyOrderAccess(socket.userId, orderId, socket.isAdmin);
      if (!hasAccess) {
        socket.emit('error', { message: '无权限发送消息到此订单' });
        return;
      }

      // 保存消息到数据库
      const message = await saveMessage(
        orderId,
        socket.userId,
        type || 'text',
        content,
        attachments,
        socket.userType || 'user'
      );

      if (message) {
        // 发送给房间内的所有用户
        const roomName = `order_${orderId}`;
        io.to(roomName).emit('new_message', {
          id: message.id,
          orderId: message.order_id,
          userId: message.user_id,
          userName: message.user_name,
          userEmail: message.user_email,
          userType: socket.userType || 'user',
          type: message.type,
          content: message.content,
          attachments: message.attachments ? (typeof message.attachments === 'string' ? JSON.parse(message.attachments) : message.attachments) : null,
          createdAt: message.created_at,
          isRead: false
        });

        console.log(`Message sent in order ${orderId} by ${socket.isAdmin ? 'admin' : 'user'} ${socket.userId}`);
      } else {
        socket.emit('error', { message: '消息发送失败' });
      }
    } catch (error) {
      console.error('Send message error:', error);
      socket.emit('error', { message: '发送消息失败' });
    }
  });
  
  // 标记消息为已读
  socket.on('mark_messages_read', async (data) => {
    try {
      const { orderId } = data;
      
      if (!socket.userId) {
        socket.emit('error', { message: '请先认证' });
        return;
      }
      
      // 标记对方发送的消息为已读
      await pool.execute(
        'UPDATE order_messages SET is_read = 1, read_at = NOW() WHERE order_id = ? AND user_id != ? AND is_read = 0',
        [orderId, socket.userId]
      );
      
      // 通知房间内的其他用户消息已被读取
      const roomName = `order_${orderId}`;
      socket.to(roomName).emit('messages_read', {
        orderId: orderId,
        readBy: socket.userId
      });
      
    } catch (error) {
      console.error('Mark messages read error:', error);
    }
  });
  
  // 断开连接
  socket.on('disconnect', () => {
    if (socket.userId) {
      onlineUsers.delete(socket.userId);
      userSockets.delete(socket.id);
      console.log(`User ${socket.userId} disconnected`);
    }
    console.log('Client disconnected:', socket.id);
  });
});

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    onlineUsers: onlineUsers.size
  });
});

// 启动服务器
const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`WebSocket server running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/health`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});
