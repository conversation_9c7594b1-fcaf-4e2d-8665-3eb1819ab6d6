{"name": "otc-websocket-server", "version": "1.0.0", "description": "WebSocket server for OTC trading platform chat", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["websocket", "socket.io", "chat", "otc"], "author": "", "license": "MIT", "dependencies": {"socket.io": "^4.7.5", "express": "^4.18.2", "cors": "^2.8.5", "mysql2": "^3.6.5", "jsonwebtoken": "^9.0.2", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2"}}